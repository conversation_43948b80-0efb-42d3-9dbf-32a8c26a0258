.story-root {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.background-image {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-color: rgb(20, 30, 40);
}

.character-image {
    position: absolute;
    bottom: 0;
    right: 10%;
    width: 400px;
    height: 600px;
    background-size: contain;
    background-position: bottom;
    background-repeat: no-repeat;
}

.dialog-container {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 20px;
    min-height: 120px;
}

.name-bar {
    background-color: rgba(100, 50, 150, 0.9);
    padding: 8px 15px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.character-name {
    color: white;
    font-size: 16px;
    font-weight: bold;
}

.dialogue-text {
    color: white;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 10px;
    white-space: normal;
}

.next-button {
    position: absolute;
    bottom: 10px;
    right: 15px;
    width: 30px;
    height: 30px;
    background-color: transparent;
    border-width: 0;
    color: white;
    font-size: 18px;
}

.choices-container {
    position: absolute;
    bottom: 200px;
    left: 50%;
    translate: -50% 0;
    flex-direction: column;
    gap: 10px;
    display: none;
}

.choice-button {
    background-color: rgba(50, 100, 200, 0.9);
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    border-width: 0;
    font-size: 14px;
    min-width: 300px;
    transition-duration: 0.2s;
}

.choice-button:hover {
    background-color: rgba(70, 120, 220, 1);
    scale: 1.02;
}

.name-input-container {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    background-color: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 10px;
    flex-direction: column;
    gap: 15px;
    display: none;
}

.name-input {
    min-width: 250px;
    padding: 10px;
    font-size: 16px;
}

.submit-button {
    background-color: rgb(50, 150, 50);
    color: white;
    padding: 12px;
    border-radius: 5px;
    border-width: 0;
}